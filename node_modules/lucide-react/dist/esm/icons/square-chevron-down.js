/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "m16 10-4 4-4-4", key: "894hmk" }]
];
const SquareChevronDown = createLucideIcon("square-chevron-down", __iconNode);

export { __iconNode, SquareChevronDown as default };
//# sourceMappingURL=square-chevron-down.js.map
